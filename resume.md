# RAJ RAVI
**GenAI Application Developer | Full-Stack Engineer**

📞 +91 8754178781  
📍 Bangalore, India  
🌐 [rajravi.dev](https://www.rajravi.dev)  
✉️ <EMAIL>  
🔗 [LinkedIn](https://www.linkedin.com/in/rajravi05/) | [GitHub](https://github.com/RajRavi05)

---

## SUMMARY
Full-stack engineer with 4+ years of experience in scalable web development and 2 years specializing in Generative AI applications. Leading development of **iBookGPT**, an AI-powered ed-tech platform at Wonderslate, including its Chrome Extension. Skilled in deploying **LangChain, OpenAI APIs, and Pinecone** into production systems, with a proven ability to deliver impact-driven, user-focused solutions.

---

## WORK EXPERIENCE

**Wonderslate Technologies – Bangalore**  
*GenAI & Full-stack Developer | 2021 – Present*

- **Lead Developer for iBookGPT:** Built and scaled an AI-powered EdTech platform leveraging LangChain, OpenAI APIs, and Pinecone, enabling personalized learning, and optimized inference pipelines for faster response times.
- **Chrome Extension Development:** Solely built and published the iBookGPT Chrome Extension, extending platform accessibility and driving user adoption.
- **Secure Systems:** Developed secure eBook/ePub readers with advanced protection to safeguard digital content.
- **Scalable APIs:** Designed and implemented APIs for data exchange, third-party integrations, and smooth interoperability across systems.
- **Automation Testing:** Applied Playwright to test critical workflows, reducing manual QA by 40%.
- **Real-time Features:** Integrated WebRTC for live collaboration and interactive experiences.
- **SEO Optimization:** Implemented strategies that boosted organic traffic by 40%+.

---

## SKILLS

- **GenAI:** LangChain, OpenAI APIs, Pinecone, LLM Integration, Prompt Engineering
- **Backend & APIs:** Node.js, Groovy/Grails, REST API Design, MySQL
- **Frontend:** React.js, JavaScript, HTML5, CSS3, Bootstrap/LESS/SASS
- **Tools:** Docker, Playwright, Git/GitHub, CVS
- **Other Expertise:** WebRTC, Chrome Extensions, SEO Optimization

---

## EDUCATION

**BE, Computer Science & Engineering**  
PCET, Anna University | 2021  
CGPA: 7.6

**Higher Secondary (12th Grade)**  
HHS, Ambur | 2017  
Percentage: 74%

---

## LANGUAGES

- English
- Tamil
- Kannada

---

## PROJECT LINKS

- [Wonderslate](https://www.wonderslate.com)
- [iBookGPT](https://ibookgpt.ai)
- [GPTSir](https://gptsir.ai)  
