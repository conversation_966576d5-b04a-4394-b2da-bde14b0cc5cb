<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nadhir.dev - Software Developer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            line-height: 1.6;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            padding: 20px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            font-size: 18px;
            font-weight: 600;
            color: #8b5cf6;
        }

        .nav {
            display: flex;
            gap: 30px;
        }

        .nav a {
            color: #ffffff;
            text-decoration: none;
            font-weight: 400;
            transition: color 0.3s ease;
        }

        .nav a:hover {
            color: #8b5cf6;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: #ffffff;
            cursor: pointer;
            font-size: 18px;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.1) 0%, rgba(10, 10, 10, 1) 70%);
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%);
        }

        .hero-content {
            display: flex;
            align-items: center;
            gap: 40px;
            max-width: 1200px;
            padding: 0 40px;
            position: relative;
            z-index: 1;
        }

        .hero-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            color: white;
            flex-shrink: 0;
        }

        .hero-text h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .hero-text .name {
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-text .role {
            color: #9ca3af;
            font-size: 48px;
            font-weight: 300;
        }

        .hero-description {
            max-width: 600px;
            margin: 30px 0;
            color: #d1d5db;
            font-size: 16px;
            line-height: 1.7;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #8b5cf6;
            color: white;
        }

        .btn-primary:hover {
            background: #7c3aed;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: #d1d5db;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            border-color: #8b5cf6;
            color: #8b5cf6;
        }

        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .social-links a {
            color: #9ca3af;
            font-size: 20px;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: #8b5cf6;
        }

        /* Services Section */
        .services {
            padding: 100px 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .services h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .services-subtitle {
            text-align: center;
            color: #9ca3af;
            margin-bottom: 60px;
            font-size: 16px;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 30px;
            transition: transform 0.3s ease, border-color 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px);
            border-color: rgba(139, 92, 246, 0.3);
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .service-card h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .service-card p {
            color: #9ca3af;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .service-list {
            list-style: none;
        }

        .service-list li {
            color: #d1d5db;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            font-size: 14px;
        }

        .service-list li::before {
            content: '•';
            color: #8b5cf6;
            position: absolute;
            left: 0;
        }

        .view-projects-btn {
            text-align: center;
            margin-top: 40px;
        }

        /* Contact Section */
        .contact {
            padding: 100px 40px;
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
            position: relative;
        }

        .contact-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .contact-text h2 {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #8b5cf6, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .contact-text p {
            color: #d1d5db;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .contact-form {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 40px;
            backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #d1d5db;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #8b5cf6;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        /* Footer */
        .footer {
            padding: 40px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .footer-left {
            color: #9ca3af;
            font-size: 14px;
        }

        .footer-right {
            color: #9ca3af;
            font-size: 14px;
        }

        .footer-social {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .footer-social a {
            color: #9ca3af;
            font-size: 18px;
            transition: color 0.3s ease;
        }

        .footer-social a:hover {
            color: #8b5cf6;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
            }

            .nav {
                gap: 20px;
            }

            .hero-content {
                flex-direction: column;
                text-align: center;
                padding: 0 20px;
            }

            .hero-text h1 {
                font-size: 36px;
            }

            .hero-text .role {
                font-size: 36px;
            }

            .services {
                padding: 60px 20px;
            }

            .services-grid {
                grid-template-columns: 1fr;
            }

            .contact {
                padding: 60px 20px;
            }

            .contact-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .footer {
                padding: 30px 20px;
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
<!-- Header -->
<header class="header">
    <div class="logo">🌟 Nadhir.dev</div>
    <nav class="nav">
        <a href="#home">Home</a>
        <a href="#projects">Projects</a>
        <a href="#contact">Contact</a>
    </nav>
    <button class="theme-toggle">🌙</button>
</header>

<!-- Hero Section -->
<section class="hero" id="home">
    <div class="hero-content">
        <div class="hero-avatar">N</div>
        <div class="hero-text">
            <h1>Hey, I'm <span class="name">Nadhir</span>✨<br>
                A <span class="role">Software Developer</span></h1>
            <div class="hero-description">
                A <strong>fullstack developer</strong> with solid foundations in <strong>design</strong>.
                Passionate about crafting seamless user experiences I thrive at the intersection
                of creativity and functionality.
            </div>
            <div class="hero-buttons">
                <a href="#contact" class="btn btn-primary">📧 Contact Me</a>
                <a href="#projects" class="btn btn-secondary">👁️ View Projects</a>
            </div>
            <div class="social-links">
                <a href="#">📱</a>
                <a href="#">💼</a>
                <a href="#">📧</a>
                <a href="#">🐦</a>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="services" id="services">
    <h2>Building Digital Experiences</h2>
    <p class="services-subtitle">I specialize in creating stunning user interfaces and developing high-quality applications that stand out.</p>

    <div class="services-grid">
        <div class="service-card">
            <div class="service-icon">💻</div>
            <h3>What I can do</h3>
            <p>I can help develop solutions that will help you grow your business</p>
            <ul class="service-list">
                <li>UI/UX Design</li>
                <li>Fullstack Web Development</li>
                <li>Mobile App Development</li>
                <li>Database Design</li>
                <li>API Integration</li>
            </ul>
        </div>

        <div class="service-card">
            <div class="service-icon">🛠️</div>
            <h3>Tools I Use</h3>
            <p>I use the latest tools and technologies to build functional and scalable products</p>
            <ul class="service-list">
                <li><strong>Frontend:</strong><br>Tailwind CSS, React, TypeScript</li>
                <li><strong>Backend:</strong><br>Node.js, Fastify, MongoDB, PostgreSQL</li>
                <li><strong>Design:</strong><br>Figma, Framer, Photoshop</li>
            </ul>
        </div>

        <div class="service-card">
            <div class="service-icon">🎨</div>
            <h3>UI/UX Design</h3>
            <p>I am a designer first, developer second. I can help design clean and modern interfaces.</p>
            <ul class="service-list">
                <li>User-Centered Design</li>
                <li>Modern & Clean UI</li>
                <li>Responsive Layouts</li>
                <li>Wireframes & Prototypes</li>
            </ul>
        </div>
    </div>

    <div class="view-projects-btn">
        <a href="#projects" class="btn btn-primary">👁️ View My Projects</a>
    </div>
</section>

<!-- Contact Section -->
<section class="contact" id="contact">
    <div class="contact-content">
        <div class="contact-text">
            <h2>Bringing your ideas to life.<br>Let's turn your vision into reality</h2>
            <p>Have a project in mind or just want to chat? Let's connect!</p>
        </div>
        <form class="contact-form">
            <div class="form-group">
                <label for="name">Name</label>
                <input type="text" id="name" name="name" placeholder="Your Name" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" placeholder="Your message here..." required></textarea>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">Send</button>
        </form>
    </div>
</section>

<!-- Footer -->
<footer class="footer">
    <div class="footer-left">
        <div><strong>Nadhir Hemil Bouthaiba</strong></div>
        <div>🇩🇿 Based in Algeria</div>
        <div>© 2024 Nadhir. All rights are reserved.</div>
    </div>
    <div class="footer-right">
        <div>Website Designed in Figma. Built with Next.js, Typescript and Tailwind CSS.</div>
        <div class="footer-social">
            <a href="#">📱</a>
            <a href="#">💼</a>
            <a href="#">📧</a>
            <a href="#">🐦</a>
        </div>
    </div>
</footer>

<script>
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Form submission
    document.querySelector('.contact-form').addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Thank you for your message! I\'ll get back to you soon.');
        this.reset();
    });

    // Theme toggle (placeholder)
    document.querySelector('.theme-toggle').addEventListener('click', function() {
        // This would toggle between light and dark themes
        console.log('Theme toggle clicked');
    });
</script>
</body>
</html>